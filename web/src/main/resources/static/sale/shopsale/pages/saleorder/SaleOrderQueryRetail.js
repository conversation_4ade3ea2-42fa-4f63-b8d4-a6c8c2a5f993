Type.registerNamespace('sale.shopsale.pages.saleorder');

sale.shopsale.pages.saleorder.SaleOrderQueryRetailAction = function () {
    sale.shopsale.pages.saleorder.SaleOrderQueryRetailAction.initializeBase(this);
    this.needQuery = false;
    this.sys = $ms.ngpConfig.Sys.userInfo;
    //是否是直营店
    this.isDirectSale = this.sys.btypeId == 0;

};

sale.shopsale.pages.saleorder.SaleOrderQueryRetailAction.prototype = {
    _deletedDetailIds: [], context: function (cb) {
        var data = {};
        //获取门店数据
        var otypeList = $shopsalecommon.getOtypeList(-1);
        var responseCashier = $common.ajaxSync({
            url: "sale/shopsale/store/getCashierList", data: "0", router: 'ngp'
        });

        var responseEypeId = $common.ajaxSync({
            url: "sale/shopsale/store/getStoreEtypeListOther", data: "0", router: 'ngp'
        });
        var cashierList = responseCashier && responseCashier.data ? responseCashier.data : [];
        this.get_form().cashierlist = cashierList;
        var etypeList = responseEypeId && responseEypeId.data ? responseEypeId.data : [];
        //默认七天
        var time = $shopsale.initQueryDateCondition();
        data.beginDate = time.startDateTime;
        data.endDate = time.endDateTime;
        var postStartList = this.getPostState();
        data.postState = postStartList[postStartList.length - 1].postState;
        data.billType = this.getBillState();
        this.needQuery = true;
        cb({
            dataSource: data,
            title: this.isDirectSale ? "门店单据查询" : "分销商销售单据查询",
            postStateDownList: postStartList,
            billType: this.getBillState(),
            payStateType: "0=未支付,1=已支付",
            otypeList: otypeList,
            etypeList: etypeList, //营业员
            cashierPersonList: etypeList, //收银员
            getPostStatusDropDown: this.getPostStatusDropDown(),
            taxEnable: ($ms.ngpConfig.Sys.sysGlobalEnabledTax && $ms.ngpConfig.Sys.sysGlobalEnabledSaleTax)
        });
    }, initialize: function SaleOrderQueryRetailAction$initialize() {
        sale.shopsale.pages.saleorder.SaleOrderQueryRetailAction.callBaseMethod(this, 'initialize');
        $shopsalecommon.addSystemLog(this, "进入门店单据查询");
        if (!this.isDirectSale) {
            //分销商开放所有权限
            var from = this.get_form();
            from.saveBill.set_enabled(true);
            from.export.set_enabled(true);
            // from.snnoStr.set_enabled(true);
            // from.batchNo.set_enabled(true);
            from.doSaveBill.set_enabled(true);
            from.confirmation.set_enabled(true);
            from.cancelPay.set_enabled(true);
            from.salePayState.set_visible(false);
        }
        this.doRefresh(true);
    }, dispose: function () {
        sale.shopsale.pages.saleorder.SaleOrderQueryRetailAction.callBaseMethod(this, 'dispose');
    }, //单据状态-列表
    getPostState: function () {
        return [{postState: 0, name: '草稿'}, {postState: 10, name: '待出入库'}, {
            postState: 7,
            name: '已出入库'
        }, {postState: 5, name: '财务核算失败'},
            {postState: 8, name: '财务核算完成'},];
    },

    getPostStatusDropDown: function () {
        var itemList = "0=草稿,100=已提交内容审核,300=内容已确认,500=待出入库,600=已出入库,650=财务核算失败,800=财务核算完成";
        return itemList;
    }, getBillState: function () {
        var billType = "";
        if (this.isDirectSale) {

            if ($ms.getPower("recordsheet.SaleBill.view")) {
                billType += 2000 + '=' + "销售出库单"
            }
            if ($ms.getPower("recordsheet.SaleBackBill.view")) {
                if (billType == '') {
                    billType += 2100 + '=' + "销售退货单"
                } else {
                    billType += "," + 2100 + '=' + "销售退货单"
                }
            }
            if ($ms.getPower("recordsheet.SaleChangeBill.view")) {
                if (billType == '') {
                    billType += 2200 + '=' + "销售换货单"
                } else {
                    billType += "," + 2200 + '=' + "销售换货单"
                }
            }

        } else {
            billType = "2000=销售出库单,2100=销售退货单,2200=销售换货单";
        }
        return billType;
    },

    queryAuditStatus: function (postSate) {
        var data = [{auditState: 1, name: '内容待审核'}, {auditState: 2, name: '内容审核中'}, {
            auditState: 3,
            name: '内容审核不通过'
        }];
        if (postSate && postSate == -1) {
            return data
        }
        return [{auditState: 4, name: '内容审核通过'}];
    }, //门店修改
    otypeChange: function () {
        var form = this.get_form();
        var data = form.saveData();
        var otypeIds = data.otypeIds;
        var items = [];
        form.cashierlist.forEach(function (value) {
            otypeIds.forEach(function (otypeIdValue) {
                if (otypeIdValue == value.otypeId) {
                    items.push(value);
                }
            });
        });
        // form.cashier.set_items(items);
    }, doOnPostStateChange: function () {
        var form = this.get_form();

        //当前单据状态
        var postState = form.postStateDown.get_value();
        //设置编辑权限
        // form.grid.getColumn("efullname").set_readOnly(postState != 0);

        var auditControl = form.auditStateDown;
        if (postState == -1 || postState == 4) {
            var list = this.queryAuditStatus(postState);
            auditControl.set_items(list);
            auditControl.set_visible(true);
            var values = list.map(function (item) {
                return item.auditState;
            });
            auditControl.set_value(values);
            return;
        }
        auditControl.set_visible(false);

    }, //收银选择时先判断门店
    cashierSeleted: function () {
        var form = this.get_form();
        var data = form.saveData();
        var otypeIds = data.otypeIds;
        if (otypeIds.length == 0) {
            $common.showTips("先选门店再选收银机");
        }
    }, //筛选
    doAllowFilter: function (sender) {
        var form = this.get_form();
        var allowed = form.grid.get_allowFilter();
        form.grid.set_allowFilter(!allowed);
    }, //商品选择
    doClickPtypeBtn: function (sender, args) {
        var text = sender.get_text();
        var params = {};
        params.showTab = false;
        params.multiSelect = false;
        params.showstopfilter = true;
        sender.set_showMDI(true); // 指定弹窗显示到当前mdi而不是全局的遮罩弹窗
        sender.set_selectorPage("jxc/baseinfo/selector/PtypeSelector.gspx");
        sender.set_selectorPageParams(params);
    },

    doGridRowDbClick: function (sender) {

        // var form = this.get_form();
        // var rowdate = form.grid.get_selectedRowData();
        // if (rowdate) {
        //     var vchcode = rowdate.vchcode;
        //     var vchtype = rowdate.vchtypeEnum;
        //     var businessTypeEnum = "EshopRetail";
        //     var postState = rowdate.postState;
        //     var url = this._getPageUrlByVchtype(vchtype);
        //     if (url) {
        //         var path = url + '?vchcode=' + vchcode + "&vchtype=" + vchtype + "&businessType=" + businessTypeEnum + "&postState=" + postState;
        //         path = $shopsale.addPageUrl(vchtype, vchcode, path);
        //         $common.showPage(sender, path);
        //     }
        // }
    }, _getPageUrlByVchtype: function (vchtypeEnum) {
        var allPageUrl = {
            /**
             * 销售出库单
             */
            Sale: "jxc/recordsheet/sale/SaleBill.gspx", /**
             * 销售退货
             */
            SaleBack: "jxc/recordsheet/sale/SaleBackBill.gspx",

            /**
             * 销售换货
             */

            SaleChange: "jxc/recordsheet/sale/SaleChangeBill.gspx",

            /**
             * 网店零售出库单
             */
            SelfSaleOrder: "jxc/recordsheet/sale/SaleBill.gspx", /**
             * 网店零售退货单
             */
            RetailSaleBack: "jxc/recordsheet/sale/SaleBackBill.gspx", /**
             * 销售出库单
             */
            EshopSale: "jxc/recordsheet/sale/SaleBill.gspx", /**
             * 销售退货
             */
            EshopSaleBack: "jxc/recordsheet/sale/SaleBackBill.gspx",

        };
        return allPageUrl[vchtypeEnum];
    }, doGridRowClick: function (sender) {
        var form = this.get_form();
        var data = form.grid.get_selectedRowData();

        if (data) {
            if (data.vchtypeEnumStr == "SaleBackBill" || data.vchtypeEnumStr == "SaleBack" || data.vchtypeEnumStr == "RetailSaleBack" || data.vchtypeEnumStr == "EshopSaleBack") {
                form.tp1.set_visible(false)
                form.tp2.set_visible(true)
                form.upBlock.set_selectedTab(form.tp2);
            }
            if (data.vchtypeEnumStr == "SaleBill" || data.vchtypeEnumStr == "Sale" || data.vchtypeEnumStr == "SelfSaleOrder" || data.vchtypeEnumStr == "EshopSale") {
                form.tp1.set_visible(true)
                form.tp2.set_visible(false)
                form.upBlock.set_selectedTab(form.tp1);
            }
            if (data.vchtypeEnumStr == "SaleChangeBill" || data.vchtypeEnumStr == "SaleChange") {
                form.tp1.set_visible(true)
                form.tp2.set_visible(true)
                form.upBlock.set_selectedTab(form.tp1);
            }

            form["grid3"].dataBind([]);
            this.doTabChange();
        }
    }, // 行绘制
    doRowRendering: function (sender, eventArgs) {
        var rowIndex = eventArgs.get_rowIndex();
        var rowData = sender.findRowData(rowIndex);
        if (rowData && rowData.ptypeId && rowData.comboRow) {
            rowData.taxRate = null;
        }
    }, doGetPriceNotShow2: function (sender, eventArgs) {
        var rowIndex = eventArgs.get_rowIndex();
        var grid = this.get_form().grid;
        var rowData = grid.get_selectedRowData();
        var data = eventArgs.get_rowData();
        var number = eventArgs.get_value();
        var currencyGivePreferentialTotal = rowData.currencyGivePreferentialTotal;
        if (currencyGivePreferentialTotal && data.paywayType == 3) {
            number = math.abs(number) + math.abs(currencyGivePreferentialTotal);
            if (eventArgs.get_value() < 0) {
                number = number * -1;
            }
            number = parseFloat(number.toFixed(2))
            eventArgs.set_text(number)
        }

        var vchtype = rowData['vchtypeEnum'];
        if (vchtype == 'SaleBackBill' || vchtype == 'SaleBill' || vchtype == 'SaleChangeBill') {
            var isadmin = $ms.ngpConfig.Sys.userInfo.isadmin;
            if (!isadmin) {
                if (!$ms.getPower("recordsheet.SaleBillQuery.checkPrice") && this.isDirectSale) {
                    eventArgs.set_text("*"); // 看也不让看
                }
            }
        }
    },

    doPTypeSelectorSelected: function (sender, args) {
        var newData = args.get_form().selectedData;
        if (!newData) {
            return;
        }

        var form = this.get_form();
        form.edPType.set_value(newData.id);
        form.edPType.set_text(newData.fullname);
    }, doCreateEtypeSelectorSelected: function (sender, args) {
        var newData = args.get_form().selectedData;
        if (!newData) return;
        var form = this.get_form();
        sender.edCreateEtype.set_value(newData.id);
        sender.edCreateEtype.set_text(newData.fullname);
    }, doEtypeSelectorSelected: function (sender, args) {
        var newData = args.get_form().selectedData;
        var affecteds = {
            edEtype: 'etypeOhterSaleBillChange', // 经手人
            edDept: 'etypeOhterSaleBillChange'   // 部门
        };
        sender.etype.change(affecteds, newData);
    }, logGridPagerDataSource: function (path, data, succeededCallback, failedCallback, userContext) {
        var form = this.get_form();
        var gird = form.grid;
        var selectItem = gird.get_selectedRowData();
        if (selectItem == null) {
            succeededCallback({itemList: [], itemCount: 0});
            return;
        }
        var pageParams = {};
        pageParams.queryParams = {vchcode: selectItem.vchcode};
        pageParams.pageSize = data.pageSize;
        pageParams.pageIndex = data.pageIndex;
        this.get_service().post("sale/shopsale/orderbill/loadBillLogList", pageParams, function (result) {
            if (result) {
                if (result.code == '200' && result.data.list) {
                    succeededCallback({itemList: result.data.list, itemCount: result.data.total});
                } else {
                    succeededCallback({itemList: [], itemCount: result.data.total});
                }
            } else {
                failedCallback();
            }
        });
    },

    //主表右键菜单
    doShowRightKeyMenu: function (sender, args) {
        var form = sender.get_form();
        var rowData = form.grid.get_selectedRowData();
        if (rowData == null) return;
        args.set_cancel(true)
        args.menu = form.itemMenu;


        form.payinfo.set_visible(false);
        form.confirmation.set_visible(false);
        form.cancelPay.set_visible(false);
        form.deleteRow.set_visible(false);
        form.logicalDeleteRow.set_visible(true);
        form.showRow.set_visible(false);
        form.payStateRow.set_visible(false);

        //已核算的单据都不可编辑 不管什么操作
        if (rowData.postState == 800) {
            if (rowData.createType == 36 && this.isDirectSale) {
                form.payinfo.set_visible(true);
            } else {
                form.payinfo.set_visible(false);
            }
            form.itemMenu.popup(args.event);
            return;
        }
        //先判断是门店订单还是自用系统
        //自用系统
        if (rowData.createType == 36) {
            //确认支付和取消支付只需判断确认支付状态
            //未确认
            if (rowData.postState == 0 || (rowData.postState == 100 && rowData.auditState == 4) || (rowData.postState >= 500 && rowData.postState < 800) || rowData.postState == 300) {
                form.confirmation.set_visible(rowData.payConfirmState == 0);
                form.cancelPay.set_visible(rowData.payConfirmState != 0);
            } else {
                form.confirmation.set_visible(false);
                form.cancelPay.set_visible(false);
            }

            //直营
            if (this.isDirectSale) {
                form.payinfo.set_visible(true);
                //删除单据只能草稿
                form.deleteRow.set_visible(rowData.postState == 0);
                //草稿、内容已确认、已提交内容审核并且未确认的 可修改
                if (rowData.postState < 500 && rowData.payConfirmState == 0) {
                    form.showRow.set_visible(true);
                } else {
                    form.showRow.set_visible(false);
                }
            } else {
                form.payinfo.set_visible(false);
                //删除单据只可直营
                form.deleteRow.set_visible(false);
                // //分销商不可修改支付方式
                // form.showRow.set_visible(false);
                //草稿并且未确认的 只修改经手人
                if (rowData.postState == 0 && rowData.payConfirmState == 0) {
                    form.showRow.set_visible(true);
                } else {
                    form.showRow.set_visible(false);
                }
            }
        } else {
            form.deleteRow.set_visible(rowData.postState == 0);
            form.logicalDeleteRow.set_visible(rowData.postState != 0);

            // 待出入库且未支付的单据-逻辑删除
            // form.logicalDeleteRow.set_visible(rowData.postState == 500 && rowData.payState == 0);
            if (rowData.postState == 0) {
                form.payStateRow.set_visible(rowData.payState == 0);
            } else {
                form.payStateRow.set_visible(false);
            }
        }

        form.itemMenu.popup(args.event);


        // if (rowData && rowData.postState == 0) {
        //     form.showRow.set_visible(true);
        //     form.deleteRow.set_visible(true);
        //     form.itemMenu.popup(args.event);
        // }
    },

    //明细表右键菜单
    doShowDetailRightKeyMenu: function (sender, args) {
        var form = sender.get_form();
        var rowData = form.grid.get_selectedRowData();
        args.set_cancel(true)
        args.menu = form.itemMenuDetail;
        //查看流水分销商才可以查看流水
        if (rowData.createType == 36 && this.isDirectSale) {
            form.payinfoTwo.set_visible(true);
            form.itemMenuDetail.popup(args.event);
        }
    },

    //修改单据
    doShowOrderDetailPage: function (sender) {
        var _that = this;
        var form = new Sys.UI.Form(sender, false);
        var formGrid = this.get_form();
        var rowdate = formGrid.grid.get_selectedRowData();
        var updateParams = new Object();
        updateParams.etypeId = rowdate.etypeId;
        updateParams.memo = rowdate.memo;
        updateParams.vchcode = rowdate.vchcode;
        updateParams.dtypeId = rowdate.dtypeId;
        updateParams.dtypeName = rowdate.dfullname;
        updateParams.summary = rowdate.summary;
        updateParams.otypeId = rowdate.otypeId;

        form.set_params(updateParams);
        form.showModal("sale/shopsale/pages/saleorder/SaleOrderEdit.gspx");
        form.add_closed(function (pop) {
            if (!pop._paramA) {
                return;
            }
            _that.doRefresh();
        });
    },

    //查看流水
    doShowPayInfo: function (sender) {
        var data = sender.get_form().grid.get_selectedRowData();
        var params = {};
        params.otypeId = data.otypeId;
        params.billNumber = data.billNumber;
        params.billDate = data.billDate;
        $common.showPage(sender, "sale/eshoporder/eshopplatformcheck/StoreFinanceCheck.gspx", params, SysConsts.CloseAll);
    },

    //确认支付
    doPayment: function (sender) {
        var _that = this;
        var formGrid = this.get_form();
        var rowdate = formGrid.grid.get_selectedRowData();
        var request = {
            vchcode: rowdate.vchcode, source: _that.isDirectSale ? "NGP" : "DISTRIBUTOR",
        };
        if (this.get_form().downGrid.payment == null || this.get_form().downGrid.payment.length == 0) {
            $common.showTips('支付信息不能为空!');
            return;
        }
        $common.showLoading();
        _that.get_service().post("sale/shopsale/zyBill/confirmPayment", request, function (response) {
            $common.hideLoading()
            if (response && response.code && response.code == '200') {
                $common.showOk('确认收款成功！');
                _that.doRefresh();
            } else {
                $common.showError('确认收款失败！');

            }
        })
    }, //取消支付
    doCancelPayment: function (sender) {
        var _that = this;
        var formGrid = this.get_form();
        var rowdate = formGrid.grid.get_selectedRowData();
        var request = {
            vchcode: rowdate.vchcode, source: _that.isDirectSale ? "NGP" : "DISTRIBUTOR",
        };
        _that.get_service().post("sale/shopsale/zyBill/cancelPayment", request, function (response) {
            if (response && response.code && response.code == '200') {
                $common.showOk('取消收款成功！');
                _that.doRefresh();
            } else {
                $common.showError('取消收款失败！');

            }
        })
    }, //充值信息详情
    doShowPayDetailPage: function (sender) {
        var _that = this;
        var form = new Sys.UI.Form(sender, 600, 600, false);
        var formGrid = this.get_form();
        var rowdate = formGrid.grid.get_selectedRowData();
        var updateParams = new Object();
        updateParams.downGrid = this.get_form().downGrid;
        updateParams.billTotal = rowdate.advanceGiftBillTotal;
        updateParams.isDirectSale = this.isDirectSale;

        form.set_params(updateParams);
        form.showModal("sale/shopsale/pages/saleorder/SaleOrderPayInfo.gspx");
        form.add_closed(function (pop) {
            if (!pop._paramA) {
                return;
            }
            formGrid.grid3.dataBind(formGrid.downGrid.payment);


            //提交审核
            // _that.doBillAudit(sender, null, null);
            _that.savePayInfo(rowdate);

        });
    },

    savePayInfo: function (rowdate) {
        var _this = this;
        var formGrid = this.get_form();
        var actions = ["保存", "取消"];
        $msg.ask("请选择对本单据的处理，按《ESC》键放弃本次处理！", actions, function (res) {
            if (actions.length === 3 && res === 0) {
                if (rowdate.postState == 800) {
                    var rest = $common.ajax({
                        url: "sale/shopsale/zyBill/cancelBill", data: rowdate.vchcode, router: 'ngp', async: false
                    });
                    if (rest.code == 200) {
                        //提交保存
                        var request = {
                            vchcode: rowdate.vchcode,
                            source: _this.isDirectSale ? "NGP" : "DISTRIBUTOR",
                            payMent: formGrid.downGrid.payment
                        };
                        _this.get_service().post("sale/shopsale/zyBill/changePaymentInformation", request, function (response) {
                            if (response && response.code && response.code == '200') {
                                $common.showOk('保存成功！');
                            } else {
                                $common.showError(response.message);
                                _this.get_form().downGrid = null;
                                _this.doTabChange();
                            }
                        })
                    } else {
                        $common.showError(rest.message);
                    }
                } else {
                    //提交保存
                    var request = {
                        vchcode: rowdate.vchcode,
                        source: _this.isDirectSale ? "NGP" : "DISTRIBUTOR",
                        payMent: formGrid.downGrid.payment
                    };
                    _this.get_service().post("sale/shopsale/zyBill/changePaymentInformation", request, function (response) {
                        if (response && response.code && response.code == '200') {
                            $common.showOk('保存成功！');
                        } else {
                            $common.showError(response.message);
                            _this.get_form().downGrid = null;
                            _this.doTabChange();
                        }
                    })
                }

            } else {
                _this.get_form().downGrid = null;
                _this.doTabChange();
            }
        }, this);

    },

    doPayState: function (sender) {
        var form = this.get_form();
        var rowdate = form.grid.get_selectedRowData();
        var _this = this;
        rowdate.vchtype = rowdate.vchtype == 2000 ? "SaleBill" : rowdate.vchtype == 2100 ? "SaleBackBill" : "SaleChangeBill"
        rowdate.businessType = "SaleNormal";
        rowdate.customRefundType = "NONE";
        rowdate.whetherPost = false;
        rowdate.number = rowdate.billNumber;
        rowdate.operationSource = "PC";

        $common.confirm("请确认是否将支付状态修改为已支付？", function (result) {
            if (!result) {
                return;
            } else {
                _this.get_service().post("sale/shopsale/bill/updateBillPayState", rowdate, function (response) {
                    if (response && response.code && response.code == '200') {
                        $common.showOk('修改成功！');
                        _this.doRefresh();
                    } else {
                        $common.showTips(response.message);
                    }
                });
            }
        });
    },

    doDeleteBill: function (sender) {
        //批量删除
        var form = this.get_form();
        var selectRowData = form.grid.get_selectedItems();

        if (!selectRowData || selectRowData.length == 0) {
            $common.showTips('请选择你要批量删除的数据！');
            return;
        } else if (selectRowData[0].postState != 0) {
            $common.showTips('只能删除草稿数据！');
            return;
        }

        // 弹出进度条窗口
        var win_form = this.get_form().importError.showModal('批量删除草稿单据', {
            errorList: [{}]
        }, {
            allowResize: true, minWidth: 706, minHeight: 500, ShowCloseButton: false,
        })
        win_form.set_showCloseButton(false);
        // 移除 ESC 关闭窗口快捷键
        win_form.removeHotkey({keyCode: Sys.UI.Key.esc});
        var billType = 'Bill';
        var vchtypeBaseName = "销售单据类";


        var unconfirmedData = [];

        for (var index = 0; index < selectRowData.length; index++) {
            var rowData = selectRowData[index];
            if (rowData.businessType === "425") {
                $common.showInfo('会员储值单据不允许修改、删除！');
                return;
            }
            // 判断状态
            var postState = parseInt(rowData.postState);
            var auditState = parseInt(rowData.postAndauditState);
            if ((postState === 0 || postState === 100) && auditState !== 2) {
                if (unconfirmedData.length >= $recordsheet.getBatchDeleteNum()) {
                    break;
                }
                var itemData = {
                    vchcode: rowData.vchcode,
                    vchtype: rowData.vchtypeEnumStr,
                    businessType: rowData.businessTypeEnum,
                    billDate: rowData.billDate,
                    billPostState: rowData.postState,
                    customType: rowData.customType,
                };
                unconfirmedData.push(itemData);
            }
        }


        // 如果要删除的为0张
        if (unconfirmedData.length === 0) {
            $common.showOk('已成功删除 0 张订单')
            return;
        }
        var url = '';
        var postData = {};
        url = 'jxc/recordsheet/billCore/batch/deleteBill';
        postData['deleteBillRequests'] = unconfirmedData;

        postData['vchtypeBaseName'] = vchtypeBaseName;
        var _this = this;
        // 批量删除订单
        $common.ajax({
            type: 'POST', url: url, router: 'ngp', async: false, data: postData, success: function (rest) {
                if (rest.code === '200') {
                    if (rest.data) {
                        var msgId = rest.data.msgId;
                        var myInterval = setInterval(function () {
                            // 轮询去查询进度信息
                            $common.ajax({
                                type: 'GET',
                                url: 'jxc/recordsheet/billCore/getOperateProgressMsg?msgId=' + msgId,
                                router: 'ngp',
                                async: false,
                                success: function (rest) {
                                    if (rest.code === '200') {
                                        if (rest.data) {
                                            var finish = rest.data.finish;
                                            if (rest.data.lineDataList && rest.data.lineDataList.length >= 0) {
                                                win_form.importErrorGrid.dataBind(rest.data.lineDataList);
                                                var lastRowData = rest.data.lineDataList[rest.data.lineDataList.length - 1];
                                                if (lastRowData && lastRowData.rowIndex) {
                                                    win_form.importErrorGrid.set_selectedRowIndex(lastRowData['rowIndex'] - 1);
                                                }
                                                if (finish) {
                                                    clearInterval(myInterval);
                                                    win_form.batchDeleteWinCancelBtn.set_enabled(true);
                                                    win_form.set_showCloseButton(true);
                                                    // 删除数据后刷新
                                                    _this.doRefresh();
                                                }
                                            }
                                        }
                                    }
                                },
                                error: function (resultData, response) {
                                    // 失败才触发
                                    console.log(resultData)
                                },
                            })
                        }, 1000);
                    }
                }
            }
        })
    },

    //删除单据
    doDelete: function (sender) {
        var form = this.get_form();
        var data = form.header.saveData();
        var rowdate = form.grid.get_selectedRowData();
        var length = 0;
        var _this = this;
        $common.confirm("单据删除后无法恢复，是否确认删除？", function (result) {
            if (!result) {
                return;
            } else {
                var request = {
                    vchcode: rowdate.vchcode,
                    vchtype: rowdate.vchtypeEnumStr,
                    businessType: rowdate.businessTypeEnum,
                    billDate: rowdate.billDate,
                    billPostState: rowdate.postState
                };
                _this.get_service().post("jxc/recordsheet/billCore/deleteBill", request, function (response) {
                    var param = new Object();
                    param.billCheck = true;
                    param.title = "错误提示";
                    if (response && response.code && response.code == '200') {
                        var data = response.data;
                        if (data.success) {
                            $common.showOk('删除成功！');
                            _this.doRefresh();
                            return;
                        } else {
                            // 删除失败，有异常抛出
                            if (data.result == 'ALLOW') {
                                param.showcontinue = true;
                            } else {
                                param.showcontinue = false;
                            }
                            param.message = data.errorDetail;
                            length = data.errorDetail.length;

                            $common.showTips(param.message);

                        }
                    } else {
                        $common.showTips('删除失败');
                    }
                });
            }
        });
    },

    //删除单据
    doLogicalDelete: function (sender) {
        var form = this.get_form();
        var data = form.header.saveData();
        var rowdate = form.grid.get_selectedRowData();
        var length = 0;
        var _this = this;
        if (!this.get_form().downGrid || this.get_form().downGrid.vchcode != rowdate.vchcode) {
            $common.showTips("单据详情获取中...请稍后");
            return;
        }
        if (this.get_form().downGrid != null) {
            ///判断是否存在paywayType为2的数据
            if (form.downGrid.payment != null) {
                var paywayType = form.downGrid.payment.filter(function (item) {
                    return item.paywayType == 2;
                });
                if (paywayType.length != 0 && rowdate.payState == 1) {
                    $common.showTips('聚合支付类型的单据不允许删除');
                    return;
                }
            }

            if (this.get_form().downGrid.relatedBill != null && this.get_form().downGrid.relatedBill.length != 0) {
                ///找到relatedBill的vchtype包含SaleBill
                var relatedBill = this.get_form().downGrid.relatedBill.filter(function (item) {
                    return item.vchtype.indexOf('SaleBill') != -1;
                });
                if (relatedBill.length == 0) {
                    $common.showTips('有关联单据不允许删除');
                    return;
                }

            }
        }


        $common.confirm("单据删除后无法恢复，是否确认删除？", function (result) {
            if (result) {
                $common.ajax({
                    url: "sale/shopsale/bill/logicalDeleteBill",
                    data: {
                        vchcode: rowdate.vchcode,
                        operationSource: 'PC'
                    },
                    waiting: "正在删除单据,请稍等片刻...",
                    owner: window,
                    async: true,
                    router: 'ngp',
                    success: function (response) {
                        if (response.code == '200') {
                            $common.showOk('删除成功！');
                            _this.doRefresh();
                            return;
                        }
                        $common.showError(response.message);
                    },
                    error: function (error) {
                        $common.showError("删除失败:" + error.message);
                    },
                });
            }
        });
    },

    //服务时间修改
    doServiceTimeChanged: function (sender) {

    },

    //详情paySelect
    paySelect: function (sender) {

    },

    //收支账户选择框
    onPaymentInit: function (sender) {
        var url = "/jxc/baseinfo/selector/PaywaySelector.gspx";
        var filterStr = sender.get_text();
        var parameter = {
            filterKey: 'quick', parTypeId: "00001", multiSelectEnabled: false,
        };
        sender.set_selectorPageParams(parameter);
        sender.set_selectorPage(url);
    },


    doPaymentSelect: function (sender, args) {
        var cform = args.get_form();
        var result = cform.selectedData;
        // SelectorColumn
        var column = sender.get_column();
        if (column) {
            var grid = column.get_grid();
            var data = grid.get_selectedRowData();
            if (!data) {
                data = {};
                data["atypeId"] = result.atypeId;
                data["atypeFullName"] = result.aFullname;
                data["currencyAtypeTotal"] = 0;
                data["paywayType"] = result.paywayType;
                data[column.get_dataField()] = result.id;
                data[column.get_displayField()] = result.fullname;
                grid.modifySelectedRowData(data);
            } else {
                grid.modifyCellValue(grid.get_selectedRowIndex(), 'paywayFullname', result.fullname);
                grid.modifyCellValue(grid.get_selectedRowIndex(), 'paywayId', result.id);
                grid.modifyCellValue(grid.get_selectedRowIndex(), 'atypeId', result.atypeId);
                grid.modifyCellValue(grid.get_selectedRowIndex(), 'paywayType', result.paywayType);
            }
        }
    },

    //营业员
    onEfullNameInit: function (sender, avgs) {
        var data = this.get_form().grid.get_selectedRowData();
        if (!data) return;
        var url = "/jxc/baseinfo/selector/EtypeSelector.gspx";
        var filterStr = sender.get_text();
        var parameter = {
            filterKey: 'quick', parTypeId: "00001", multiSelectEnabled: false,
        };
        sender.set_selectorPageParams(parameter);
        sender.set_selectorPage(url);
    }, //选择营业员
    doEfullNamSelect: function (sender, args) {
        args.set_focusNext(false);  // 光标不自动下移

        var cform = args.get_form();
        var result = cform.selectedData;
        // SelectorColumn
        var grid = this.get_form().grid;
        var data = grid.get_selectedRowData();

        data.efullname = result.fullname;
        data.etypeId = result.id;
        grid.modifyCellValue(grid.get_selectedRowIndex(), 'efullname', result.fullname);
        grid.modifyCellValue(grid.get_selectedRowIndex(), 'etypeId', result.id);
        this.doBillAudit(sender, null, null);
    },

    doEfullNamChange: function (sender) {
        var cform = this.get_form();

        var grid = cform.grid;
        var data = grid.get_selectedRowData();
        if (sender.get_text() != "" || data.etypeId == 0) return;

        // SelectorColumn


        data.efullname = "";
        data.etypeId = 0;
        grid.modifyCellValue(grid.get_selectedRowIndex(), 'efullname', "");
        grid.modifyCellValue(grid.get_selectedRowIndex(), 'etypeId', 0);
        this.doBillAudit(sender, null, null);
    }, //现价改变
    onBlurWithPrice: function (sender, avgs, itemData) {
        var column = sender.get_column();
        var grid = column.get_grid();
        var data = grid.get_selectedRowData();
        var items = grid.saveData();
        var mainData = this.get_form().grid.get_selectedRowData();

        //计算价格
        var priceTotal = data.currencyPrice * data.unitQty;
        if (priceTotal == 0) {
            data.currencyDisedTaxedPrice = 0;
            var cell = grid.getRowCell(grid.get_selectedRowIndex(), 'currencyPrice');
            $common.showHint("请输入折前单价", cell);
            grid.modifySelectedRowData(data);
            return;
        }

        this.calculatePrice(sender.get_text(), data);
        //刷新
        grid.modifySelectedRowData(data);

        var billTotal = 0;
        for (var i = 0; i < items.length; i++) {
            var item = items[i];
            billTotal += item.currencyDisedTaxedTotal;
        }
        mainData.advanceGiftBillTotal = billTotal;
        this.get_form().grid.modifySelectedRowData(mainData);
    },


    //提交审核
    doBillAudit: function (sender, avgs, dataItem) {


        var _this = this;
        var form = sender.get_form();
        var data = dataItem ? dataItem : form.grid.get_selectedRowData();
        if (!data) return;
        if (data.postState == 0) {
            //草稿不处理保存
            return;
        }
        if (data.postState == 800) {
            $common.showError("已核算单据不允许保存");
            return;
        }


        if (!data) return;
        _this.doSaveDetails(sender, false, data);
    },

    //批量核算
    doSaveBill: function (sender) {
        var form = this.get_form();
        var gird = form.grid;
        var selectItems = gird.get_selectedItems();
        if (selectItems.length == 0) {
            $common.showTips('请选择审核通过的单据进行批量保存操作!');
            return;
        }


        var vchcodeList = [];
        var vchtypes = [];
        var notPowerConut = 0;
        var notConfirmConut = 0;
        var notPayConut = 0;

        var poWerObj = {};
        for (var index = 0; index < selectItems.length; index++) {
            // 允许待出入库(500)/已出入库(600)/财务核算失败(5)的单据进行批量核算
            if (selectItems[index].postState != 500 && selectItems[index].postState != 600 && selectItems[index].postState != 650) {
                $common.showTips('请选择待出入库/已出入库/财务核算失败的单据进行批量核算操作!');
                return;
            }
            var vchTypeEnums = selectItems[index].vchtype == 2000 ? "Sale" : "SaleBack";
            var powerObj = $shopsale._getPowerByVchtype(vchTypeEnums, null, selectItems[index].customType);
            if (!powerObj.saveBill) {
                notPowerConut++;
                poWerObj[selectItems[index].vchtype] = true;
                continue;
            }
            if (!vchtypes.includes(selectItems[index].vchtype)) {
                vchtypes.push(selectItems[index].vchtype)
            }
        }
        // //验证哪些单据开启了审核
        // var rest1 = $common.ajax({
        //     url: "jxc/recordsheet/saveBillBatch/checkVchtype",
        //     data: {vchtypes: vchtypes},
        //     router: 'ngp',
        //     async: false
        // });
        // var audits = rest1.data.audits
        // var auditObj = {}
        // for (var a = 0; a < audits.length; a++) {
        //     if (!audits[a].stoped) {
        //         auditObj[audits[a].vchtype] = true
        //     }
        // }
        var needAuditCount = 0;
        for (var index = 0; index < selectItems.length; index++) {
            var vchtype = selectItems[index].vchtype
            // if (auditObj[vchtype]) {
            //     if (selectItems[index].postAndauditStateName != "审核通过" && selectItems[index].postAndauditStateName != "已确认") {
            //         needAuditCount++;
            //         continue;
            //     }
            // }
            // if (poWerObj[vchtype]) {
            //     continue;
            // }
            //支付状态为未支付的不可核算
            // if (selectItems[index].payConfirmState == 0) {
            //     notConfirmConut++;
            //     continue;
            // }
            if (selectItems[index].payState == 0) {
                notPayConut++;
                continue;
            }

            vchcodeList.push(selectItems[index].vchcode);
        }
        if (vchcodeList.length == 0) {
            $common.showTips('请选择审核通过，已支付，且有保存过账权限的单据进行批量保存操作!');
            return;
        }
        //验证这些选择的单据 有多少已确认
        var rest = $common.ajax({
            url: "jxc/recordsheet/saveBillBatch/checkConfirmCount",
            data: {vchcodeList: vchcodeList},
            router: 'ngp',
            async: false
        });
        if (rest.code == "200") {
            if (!rest.data.successed) {
                $common.showTips('本次选择的单据全部为核算完成单据,请重新选择!');
                return;
            }
            $common.confirm("您正在批量保存单据，已自动过滤已过账单据" + rest.data.value + "张，未确认收款的单据" + notConfirmConut + "张,未支付的单据" + notPayConut + "张,未审核完成单据" + needAuditCount + "张,无保存过账权限单据" + notPowerConut + "张,是否继续批量保存单据？", function (res) {
                if (res) {
                    // var restData = $common.ajax({
                    //     url: "jxc/recordsheet/saveBillBatch/batchSave",
                    //     data: {vchcodeList: vchcodeList, vchtypes: vchtypes, createType: 1},
                    //     router: 'ngp',
                    //     async: false
                    // });

                    var restData = $common.ajax({
                        url: "sale/shopsale/bill/batchPost", data: vchcodeList, router: 'ngp', async: false
                    });
                    if (restData.code == "200") {

                        var newForm = new Sys.UI.Form(sender);
                        var url = "jxc/recordsheet/common/ShowBatchDetailManager.gspx";
                        newForm.showMDI(url, {"batchId": restData.data.value.id});
                        newForm.add_close(function () {
                            if (this.hasEvent('refresh')) {
                                this.event('refresh');
                            }
                            this.doRefresh(true);
                        }, this)
                    } else {
                        $common.showError('提交批量保存错误!');
                    }
                    return;

                }
            }, this);
        }
    }, doPagerDataSource: function (path, data, succeededCallback, failedCallback, userContext) {
        if (!this.needQuery) {
            succeededCallback({itemList: [], itemCount: 0});
            return;
        }
        var query = {};
        try {
            query.queryParams = this._getQueryData();
        } catch (e) {
            failedCallback({
                get_message: function () {
                    return e;
                }
            });
            return;
        }
        if (query.queryParams.vchtypes.length == 0) {
            $common.showTips("单据类型不能为空!");
            failedCallback({});
            return
        }

        if (query.queryParams.postStateList && query.queryParams.postStateList.length == 0) {
            $common.showTips("单据状态不能为空!");
            failedCallback({});
            return
        }
        if (query.queryParams.endTime != null && query.queryParams.startTime != null) {
            var a = new Date(Date.parse(query.queryParams.startTime));
            var b = new Date(Date.parse(query.queryParams.endTime));
            if (a > b) {
                $common.showTips("开始时间不能大于结束时间");
                failedCallback({});
                return
            }
        }
        data.queryParams ? (data.queryParams.log ? query.queryParams.log = data.queryParams.log : '') : '';
        query.pageSize = data.pageSize;
        query.pageIndex = data.pageIndex;
        query.sorts = data.sorts;
        query.queryParams.gridFilter = data.queryParams.gridFilter;


        var that = this;
        this.get_service().post("sale/shopsale/billcore/getSaleOrderList", query, function (result) {
            if (result.code == 200) {
                var list = result.data.list;
                Array.forEach(list, function (item) {
                    if (item.needInvoice != null && item.needInvoice == 0) {
                        that.clearInvoiceInfo(item);
                    }
                })

                succeededCallback({itemList: list, itemCount: result.data.total});
                that.doTabChange();
                //修改合计的展示
                that._countSummary(result.data);
            } else {
                $common.showTips(result.message);
                failedCallback();
            }
        });
    },

    clearInvoiceInfo: function (item) {
        item.invoiceTax = null;
        item.invoiceTitle = null;
        item.invoiceType = null;
        item.invoiceUserType = null;
    },

    _countSummary: function (data) {
        var priceNotShow = null;
        var isadmin = $ms.ngpConfig.Sys.userInfo.isadmin;
        if (!isadmin) {
            if (!$ms.getPower("recordsheet.SaleBillQuery.checkPrice") && this.isDirectSale) {
                priceNotShow = '*';
            }
        }
        var summaryItems = {
            "currencyBillTotal": {
                "value": priceNotShow

            }, "balanceSettled": {
                "value": priceNotShow

            }, "currencyBalanceRemain": {
                "value": priceNotShow

            }, "currencyBuyerFreightFee": {
                "value": priceNotShow

            }
        };
        var grid = this.get_form().grid;
        for (var key in summaryItems) {
            if (grid.findColumn(key)) {
                if (summaryItems[key].value == '*') {
                    grid.findColumn(key).set_footer(summaryItems[key].value)
                }
            }
        }
    }, _getQueryData: function () {
        var form = this.get_form();
        var data = form.saveData();
        var queryParams = {};

        //单据类型
        var saleOrderTypeItems = form.saleOrderType.get_value();
        //支付状态
        var salePayStateItems = form.salePayState.get_value();
        var businessTypeList = [];
        businessTypeList.push(201);
        businessTypeList.push(202);
        if (!this.isDirectSale) {
            businessTypeList.push(203);
        }
        var vchtypes = [];
        if (saleOrderTypeItems.length > 0) {
            for (var a = 0; a < saleOrderTypeItems.length; a++) {
                var item = saleOrderTypeItems[a];
                vchtypes.push(item)
            }
        }
        if (salePayStateItems.length == 0) {
            $common.showTips("支付状态不能为空!");
        } else if (salePayStateItems.length == 1) {
            queryParams.payState = salePayStateItems[0];
        } else {
            queryParams.payState = -1;
        }
        queryParams.businessTypeList = businessTypeList;
        queryParams.vchtypes = vchtypes;

        if (data.postState == -1 || data.postState == 4) {
            queryParams.postStateList = form.auditStateDown.get_value();
            if (queryParams.postStateList.length == 0) {
                throw "审核状态不能为空！";
            }
        } else {
            queryParams.postStateList = [data.postState];
        }

        // queryParams.postStateList = data.postState == -1 ? [1, 2, 3] : [data.postState];
        data.beginDate ? queryParams.startTime = data.beginDate : "";
        data.endDate ? queryParams.endTime = data.endDate : "";
        //收银员
        data.etypeid ? queryParams.createEtypeId = data.etypeid : "";
        //营业员
        data.createEtypeId ? queryParams.etypeid = data.createEtypeId : "";
        //门店列表
        data.otypeIds ? queryParams.otypeIds = data.otypeIds : "";
        //商品
        data.ptypeid ? queryParams.ptypeId = data.ptypeid : "";
        //单据编号
        data.number ? queryParams.number = data.number : "";
        //收银台
        data.cashierIds ? queryParams.cashierIds = data.cashierIds : "";
        //会员手机号
        data.vipPhone ? queryParams.vipPhone = data.vipPhone : "";
        //会员名
        data.vipName ? queryParams.vipName = data.vipName : "";
        //sku
        data.skuId ? queryParams.skuId = data.skuId : "";
        queryParams.ownBtypeId = this.sys.btypeId;//= "818146700297822200";//

        queryParams.saleOrbuy = 2;
        queryParams.isAdmin = false;
        queryParams.checkStatus = -1;
        queryParams.queryType = "DirectSales";
        // 支付方式
        var paywayList = this.payRelationAction.getSelectValues();
        if (paywayList) {
            queryParams.paywayIds = paywayList.map(function (item) {
                return item.paywayId;
            });
        }

        return queryParams;
    }, doTabChange: function (sender, eventArgs) {
        var tab = this.get_form().upBlock.get_selectedTab();
        var form = this.get_form();
        var itemIndex = "";
        form.upBlock.set_visible(true);
        var grid = null;
        switch (tab.get_idPart()) {
            case "tp0":
                form.logMessage.get_pager().refresh();
                break;
            case "tp1":
                grid = form.grid0;
                itemIndex = 0;
                break;
            case "tp2":
                grid = form.grid1;
                itemIndex = 1;
                break;
            case "tp3":
                grid = form.grid2;
                itemIndex = 2;
                break;
            case "tp4":
                grid = form.grid3;
                itemIndex = 3;
                break;
            case "tp5":
                grid = form.grid4;
                itemIndex = 4;
                break;
        }
        if (!grid && itemIndex != 4) return;
        var rowdate = form.grid.get_selectedRowData();
        if (rowdate) {
            if (itemIndex == 3) {
                if (rowdate.vchtypeEnum == "SaleBackBill") {
                    grid.getColumn("atypeFullName").set_caption("付款账户");
                    grid.getColumn("currencyAtypeTotal").set_caption("付款金额");

                } else {
                    grid.getColumn("atypeFullName").set_caption("收款账户");
                    grid.getColumn("currencyAtypeTotal").set_caption("收款金额");
                }
                // grid.getColumn("paywayId").set_readOnly(rowdate.postState != 0);
                // grid.getColumn("outNo").set_readOnly(rowdate.postState != 0);
            }
            if (itemIndex == 1) {
                grid.getColumn("batchNo").set_readOnly(rowdate.postState == 800);
                grid.getColumn("snnoStr").set_readOnly(rowdate.postState == 800);
                grid.getColumn("produceDate").set_readOnly(rowdate.postState == 800);
                // grid.getColumn("serviceEndTime").set_readOnly(rowdate.postState != 0);
            }

            //判断是否已请求过下表体数据
            if (!this.get_form().downGrid || this.get_form().downGrid.vchcode != rowdate.vchcode || form.doRefreshBth == true) {
                form.doRefreshBth = false;
                var vchcode = rowdate.vchcode;
                var vchtypeEnum = rowdate.vchtypeEnumStr;
                var queryparm = {};
                queryparm.vchcode = vchcode;
                queryparm.vchtype = vchtypeEnum;
                var that = this;
                this.get_service().post("sale/shopsale/bill/getGoodsBill", queryparm, function (rest) {
                    if (rest.code == 200) {

                        var getBillDetailResponse = rest.data
                        var outDetail = getBillDetailResponse.outDetail;
                        for (var i = 0; i < outDetail.length; i++) {
                            var detail = outDetail[i];
                            if (detail.vchtype === "Sale") {
                                detail.vchtype = "SaleBill";
                            } else if (detail.vchtype === "SaleBack") {
                                detail.vchtype = "SaleBackBill";
                            }
                        }

                        that.get_form().downGrid = getBillDetailResponse;
                        that.get_form().downGrid.vchcode = rowdate.vchcode;
                        that._bindDownGrid(that.get_form().downGrid, itemIndex);

                    }
                })
            } else {
                this._bindDownGrid(this.get_form().downGrid, itemIndex);

            }

        } else {
            form["grid" + itemIndex].dataBind([]);
            form.logMessage.dataBind([]);
        }
    },

    doExport: function (sender) {
        var form = sender.get_form();
        var data = form.grid.saveData();
        if (!data || data.length == 0) {
            throw Error.message('没有数据可以导出，请更换查询条件再试');
        }

        var sessionKey = "sale." + form.get_caption() + "_needSms";
        var needSms = sessionStorage.getItem(sessionKey);
        if (needSms == null) {
            needSms = true;
        } else {
            needSms = JSON.parse(needSms);
        }

        var queryParams = this._getQueryData();
        if (!queryParams) {
            return
        }
        queryParams.gridFilter = form.grid.get_pager().get_queryParams().gridFilter;
        var exportConfirmForm = new Sys.UI.Form(sender, true);
        var header = this.getExportHeader(sender, form.get_caption());
        // 获取当前排序条件，放入header中
        header.sortFields = form.grid.getSortedOrders();

        var exportRequest = {
            reportName: "sale-export|pos-saleOrder",
            source: this.getPageName(sender.get_form()),
            params: JSON.stringify(queryParams),
            header: JSON.stringify(header),
            startTime: queryParams.startTime,
            endTime: queryParams.endTime,
            //短信验证
            needSms: needSms,
            //导出数据单选组
            exportData: false,
            //内容涵盖单选组中是否需要【导出商品信息】按钮
            contentContainOnlyDetails: false
        };
        exportConfirmForm.showModal("/exportcenter/ui/ExportConfirm.gspx?title=" + form.get_caption(), exportRequest);
    },

    doExportList: function (sender, args) {
        var exportListForm = new Sys.UI.Form(sender, true);
        exportListForm.showModal("/exportcenter/ui/ExportLogList.gspx?source=" + this.getPageName(sender.get_form()));
    },

    getPageName: function (form) {
        return "sale." + form.get_caption();
    },

    getExportHeader: function (sender, reportName) {
        var form = sender.get_form();
        var grid = form.grid;
        var grid0 = form.grid0;
        var dataObj = {};
        dataObj.needImage = true;
        dataObj.reportName = reportName;
        dataObj.masterFields = [];
        $export.initMasterFields(form, grid, dataObj.masterFields);
        if (grid) {
            dataObj.detailFields = [];
            $export.initDetailFields(grid, dataObj.detailFields);
            dataObj.detailFields.push({
                name: "支付方式",
                caption: "支付方式",
                dataField: "paymentStr",
                columnWidth: 63,
                headerAlign: "",
                textAlign: ""
            })
            var detailFields = [];
            $export.initDetailFields(grid0, detailFields);
            detailFields.push({
                name: "明细类型",
                caption: "明细类型",
                dataField: "detailType",
                columnWidth: 63,
                headerAlign: "",
                textAlign: ""
            });
            for (var i = 0; i < detailFields.length; i++) {
                // 第一个是行号
                if (i == 0) continue;
                // 与主体冲突字段处理
                if (detailFields[i].dataField == "currencyOrderPreferentialAllotTotal") {
                    detailFields[i].dataField = "detailCurrencyOrderPreferentialAllotTotal";
                }
                if (detailFields[i].dataField == "currencyPreferentialTotal") {
                    detailFields[i].dataField = "detailCurrencyPreferentialTotal";
                }
                if (detailFields[i].dataField == "currencyGivePreferentialTotal") {
                    detailFields[i].dataField = "detailCurrencyGivePreferentialTotal";
                }
                if (detailFields[i].dataField == "currencyPtypePreferentialTotal") {
                    detailFields[i].dataField = "detailCurrencyPtypePreferentialTotal";
                }
                if (detailFields[i].dataField == "memo") {
                    detailFields[i].dataField = "detailMemo";
                }
                dataObj.detailFields.push(detailFields[i]);
            }
        }
        return dataObj;
    },

    changeInDetailBatchNo: function (sender) {
        // 入库明细修改了批次后，要更新doRefreshBth，防止tab切换时批次没还原
        var form = sender.get_form();
        form.doRefreshBth = true;
        var data = form.grid1.get_selectedRowData();
        data.batchno = data.batchNo;
    },

    doRefreshBtn: function (openPage) {
        var form = this.get_form();
        form.doRefreshBth = true;
        this.doRefresh(openPage);
    }, //刷新
    doRefresh: function (openPage) {
        if (!this.needQuery) {
            return;
        }
        var form = this.get_form();
        var queryParams = {};
        try {
            queryParams = this._getQueryData();
        } catch (e) {
            $common.showTips("筛选条件设置错误");
            return;
        }
        if (!queryParams || !queryParams.vchtypes) {
            return;
        }
        if (openPage == true) {
            queryParams.log = '进入【' + form.get_caption() + '】';
        }

        // 控制批量删除按钮显示，只在查询草稿状态时显示
        if (queryParams.postStateList && queryParams.postStateList.length === 1 && queryParams.postStateList[0] === 0) {
            form.deleteBill.set_visible(true);
        } else {
            form.deleteBill.set_visible(false);
        }

        var startObj = null;
        if (window.$startTiming) {
            startObj = $startTiming('云零售->pc/门店单据查询->查询');
        }
        form.grid.get_pager().refresh(queryParams);
        if (null != startObj) {
            startObj.endTiming();// 触发上报
        }
    }, _bindDownGrid: function (downGrid, itemIndex) {
        var form = this.get_form();
        var detail = "";

        switch (itemIndex) {
            case 0:
                detail = downGrid.outDetail ? downGrid.outDetail : [];
                var snnoStr = "";
                Array.forEach(detail, function (item) {
                    item.unitQty = item.unitQty || item.unitQty === 0 ? math.abs(item.unitQty) : null;
                    item.currencyDisedTotal = item.currencyDisedTotal ? math.abs(item.currencyDisedTotal) : 0;
                    item.currencyDisedTaxedTotal = item.currencyDisedTaxedTotal ? math.abs(item.currencyDisedTaxedTotal) : 0;
                    item.currencyTotal = item.currencyTotal ? math.abs(item.currencyTotal) : 0;
                    for (var i = 0; i < item.serialNoList.length; i++) {
                        snnoStr += item.serialNoList[i].snno == null ? "" : item.serialNoList[i].snno + ",";
                        item.serialNoList[i].snRemark = item.serialNoList[i].snMemo;
                    }
                    item.snnoStr = snnoStr.trimEndChar(",");
                    item.qty = item.unitQty;
                    item.batchno = item.batchNo;
                    snnoStr = "";
                });
                break;
            case 1:
                detail = downGrid.inDetail ? downGrid.inDetail : [];
                var snnoStr = "";
                Array.forEach(detail, function (item) {
                    item.unitQty = item.unitQty ? math.abs(item.unitQty) : null;
                    item.currencyDisedTotal = item.currencyDisedTotal ? math.abs(item.currencyDisedTotal) : 0;
                    item.currencyDisedTaxedTotal = item.currencyDisedTaxedTotal ? math.abs(item.currencyDisedTaxedTotal) : 0;
                    item.currencyTotal = item.currencyTotal ? math.abs(item.currencyTotal) : 0;

                    for (var i = 0; i < item.serialNoList.length; i++) {
                        snnoStr += item.serialNoList[i].snno == null ? "" : item.serialNoList[i].snno + ",";
                        item.serialNoList[i].snRemark = item.serialNoList[i].snMemo;
                    }
                    item.snnoStr = snnoStr.trimEndChar(",");
                    item.qty = item.unitQty;
                    item.batchno = item.batchNo;
                    snnoStr = "";
                });
                break;
            case 2:
                detail = downGrid.fee ? downGrid.fee : [];
                break;
            case 3:
                detail = downGrid.payment ? downGrid.payment : [];
                if (detail.length == 0 && downGrid.mark != null && downGrid.mark.length != 0) {
                    for (var i = 0; i < downGrid.mark.length; i++) {
                        var mark = downGrid.mark[i];
                        if (mark.markCode == "60000001") {
                            detail.push({
                                "atypeFullName": "未知", "currencyAtypeTotal": 0, "paywayFullname": "积分兑换"
                            })
                        }
                        if (mark.markCode == "60000002") {
                            detail.push({
                                "atypeFullName": "未知", "currencyAtypeTotal": 0, "paywayFullname": "充值赠送"
                            })
                        }
                    }
                }
                break;
            case 4:
                detail = downGrid.relatedBill ? downGrid.relatedBill : [];
        }
        form["grid" + itemIndex].dataBind(detail);
    },

    doSnSelected: function (sender, eventArgs) {
        eventArgs.set_focusNext(false);

        var ptypeList = eventArgs.get_form().ptypeList;
        if (ptypeList.length < 1) {
            return;
        }
        var grid;
        var form = sender.get_form();
        var tab = this.get_form().upBlock.get_selectedTab();
        var data = form.grid.get_selectedRowData();
        if (data) {
            if (tab.get_idPart() == "tp2") {
                grid = sender.get_form().grid1;
            } else {
                grid = sender.get_form().grid0;
            }
        } else {
            eventArgs.set_cancel(true);
            return;
        }
        var index = grid.get_selectedRowIndex();
        var selectRowData = grid.get_selectedRowData();
        var rowData = ptypeList[0].rowData;
        if (rowData.serialNoList.length > selectRowData.unitQty) {
            $common.showWarn("序列号数量不能超过明细数量");
            return;
        }

        var datas = grid.saveData();
        var snnos = [];
        for (var i = 0; i < datas.length; i++) {
            if (datas[i].serialNo && datas[i].serialNo.length > 0) {
                for (var j = 0; j < datas[i].serialNo.length; j++) {
                    if (datas[i].detailId != selectRowData.detailId) {
                        snnos.push(datas[i].serialNo[j].snno);
                    }
                }
            }
        }
        if (snnos.length > 0) {
            for (var i = 0; i < snnos.length; i++) {
                if (rowData.serialNoList && rowData.serialNoList.length > 0) {
                    for (var j = 0; j < rowData.serialNoList.length; j++) {
                        if (snnos[i] == rowData.serialNoList[j].snno) {
                            $common.showWarn(rowData.serialNoList[j].snno + "序列号已存在！");
                            return;
                        }
                    }
                }
            }
        }
        var snnoStr = "";
        if (rowData.serialNoList.length > 0) {
            for (var i = 0; i < rowData.serialNoList.length; i++) {
                snnoStr += rowData.serialNoList[i].snno == null ? "" : rowData.serialNoList[i].snno + ",";
            }

            // 返回数据有批次号且与当前行批次号不同，则覆盖现有的
            if (rowData.serialNoList[0].batchNo && rowData.serialNoList[0].batchNo != selectRowData.batchno) {
                selectRowData.batchno = rowData.serialNoList[0].batchNo;
                selectRowData.produceDate = rowData.serialNoList[0].produceDate;
                selectRowData.protectDays = rowData.serialNoList[0].protectDays;
                selectRowData.expireDate = rowData.serialNoList[0].expireDate;
                selectRowData.costId = rowData.serialNoList[0].costId;
            }
        }
        selectRowData.snNoStr = snnoStr.trimEndChar(",");
        selectRowData.snnoStr = snnoStr.trimEndChar(",");
        selectRowData.batchNo = selectRowData.batchno;
        selectRowData.serialNoList = rowData.serialNoList;
        selectRowData.modify = true;

        grid.modifyRowData(index, selectRowData);
    }, doCheckModifyOut: function (sender, eventArgs) {
        var grid = sender.get_form().grid0;
        var form = sender.get_form();
        var data = form.grid.get_selectedRowData();
        if (!data) {
            eventArgs.set_cancel(true);
            return;
        }

        var mainGrid = sender.get_form().grid;
        var bill = mainGrid.get_selectedRowData();
        var item;
        if (grid) {
            item = grid.get_selectedRowData();
        }
        if (item == null) {
            eventArgs.set_cancel(true);
            return;
        }
        item.ptype = "";
        item.snEnabled = item.snenabled;

        if (eventArgs._column.get_dataField() == 'snnoStr') {
            //序列号
            //没开启序列号，已核算，已审核并且非第三方发货
            if (!item.snEnabled || bill.postState != 500) {
                eventArgs.set_cancel(true);
            }
        }
        if (eventArgs._column.get_dataField() == 'batchNo') {
            //批次号
            if (!item.batchenabled || bill.postState != 500) {
                eventArgs.set_cancel(true);
            }
        }
    }, doCheckRendering: function (sender, args) {
        var column = args.get_column();
        var field = column.get_dataField();
        if (field != 'batchNo' && field != 'snnoStr') {
            return;
        }
        var mainGrid = sender.get_form().grid;
        var bill = mainGrid.get_selectedRowData();
        if (bill == null) return;
        var rowIndex = args.get_rowIndex();
        var rowData = sender.findRowData(rowIndex);
        if (field == 'batchNo' && (!rowData.batchenabled || bill.postState != 500)) {
            args.set_cssClass('HideButton');
        }
        if (field == 'snnoStr' && (!rowData.snenabled || bill.postState != 500)) {
            args.set_cssClass('HideButton');
        }

    }, doCheckModifyIn: function (sender, eventArgs) {
        var grid = sender.get_form().grid1;
        var form = sender.get_form();
        var data = form.grid.get_selectedRowData();
        if (!data) {
            eventArgs.set_cancel(true);
            return;
        }

        var mainGrid = sender.get_form().grid;
        var bill = mainGrid.get_selectedRowData();
        var item;
        if (grid) {
            item = grid.get_selectedRowData();
        }
        if (item == null) {
            eventArgs.set_cancel(true);
            return;
        }
        item.ptype = "";
        item.snEnabled = item.snenabled;

        if (eventArgs._column.get_dataField() == 'snnoStr') {
            //序列号
            //没开启序列号，已核算，已审核并且非第三方发货
            if (!item.snEnabled || bill.postState != 500) {
                eventArgs.set_cancel(true);
            }
        }
        if (eventArgs._column.get_dataField() == 'batchNo') {
            //批次号
            if (!item.batchenabled || bill.postState != 500) {
                eventArgs.set_cancel(true);
            }
        }
        if (eventArgs._column.get_dataField() == 'produceDate') {
            //生产日期
            if (!item.batchenabled || bill.postState != 500) {
                eventArgs.set_cancel(true);
            }
        }
    }, checkProcessStateIsHaveAudit: function (sender, msg) {
        if (!sender || !sender.get_form() || !sender.get_form().grid || !sender.get_form().grid.get_selectedRowData() || !sender.get_form().grid.get_selectedRowData().state || !sender.get_form().grid.get_selectedRowData().state.processState) {
            return true;
        }
        var processState = sender.get_form().grid.get_selectedRowData().state.processState;
        if (processState == 'AUDIT') {
            return false;
        }
        // $common.showTips(msg);
        return true;
    },


    //保存单据明细
    doSaveDetails: function (sender, audit, mainData) {
        var form = sender.get_form();
        if (mainData == null) {
            return;
        }
        var _this = this;
        var detailGrid;
        // 依据目前的设计，保存单据明细只能保存一个tab页的数据，也就是tab标签停留在哪个就去保存哪个的数据
        // 出库明细 tp1，入库明细 tp2，其他的不处理
        var tab = _this.get_form().upBlock.get_selectedTab();
        if (mainData) {
            if (tab.get_idPart() == "tp2") {
                detailGrid = sender.get_form().grid1;
            } else {
                detailGrid = sender.get_form().grid0;
            }
            // if (mainData.vchtypeEnum == "SaleBack" || mainData.vchtypeEnum == "SaleBackBill" || mainData.vchtypeEnum == "EshopSaleBack") {
            //     detailGrid = sender.get_form().grid1;
            // } else {
            //     detailGrid = sender.get_form().grid0;
            // }
        }

        var paymenyData = sender.get_form().grid3.saveData();

        if (mainData.postState == 800) {
            $common.showError("已核算单据不允许保存");
            return;
        }

        form.saveDetails.set_enabled(false);
        var items = detailGrid.saveData();
        if (items.length == 0) {
            $common.showOk("保存成功！");
            form.saveDetails.set_enabled(true);
            return;
        }

        // var total = 0;
        for (var i = 0; i < items.length; i++) {
            var item = items[i];
            if (item.combo) continue;
            // var disedTaxedTotal = $jarvismoney._doCalculate(item.disedTaxedTotal);
            // total += disedTaxedTotal;
            if (item.snenabled) {
                var num = item.unitQty;
                var num2 = math.floor(parseFloat(num));
                if (num - num2 > 0) {
                    $common.alert("序列号商品数量不能为小数,请重新编辑后保存！");
                    form.saveDetails.set_enabled(true);
                    return false;
                }
                var snnoCount = item.serialNoList ? item.serialNoList.length : 0;
                if (snnoCount > num) {
                    $common.alert("序列号数量不能大于商品数量,请重新编辑后保存！");
                    form.saveDetails.set_enabled(true);
                    return false;
                }
            }
            // if (item.batchenabled) {
            //     var expireDate = Date.parse(item.expireDate);
            //     item.expireDate = "2023-12-25T16:00:00.000+00:00";
            //     var produceDate = Date.parse(item.produceDate);
            //     item.produceDate = "2022-12-25T16:00:00.000+00:00";
            // }
        }

        if (this.get_form().downGrid == null) {
            form.saveDetails.set_enabled(true);
            return
        }
        if (tab.get_idPart() == "tp2") {
            this.get_form().downGrid.inDetail = items;
        } else {
            this.get_form().downGrid.outDetail = items;
        }

        var request = {};
        request.businessType = "SaleNormal";
        request.vchcode = mainData.vchcode;
        request.vchtype = mainData.vchtype == 2000 ? "SaleBill" : "SaleBackBill";
        request.orderSaleMode = 6;
        request.customType = 0;
        request.postState = mainData.postState;

        var options = {
            url: "sale/shopsale/bill/getGoodsBill", data: request, success: function (res, response) {
                if (res.code == "200") {
                    if (tab.get_idPart() == "tp2") {
                        Array.forEach(_this.get_form().downGrid.inDetail, function (item) {
                            if (item.serialNo != null && item.serialNo.length > 0) {
                                item.serialNoList = item.serialNo;
                            }
                        });
                        res.data.inDetail = _this.get_form().downGrid.inDetail;
                    } else {
                        Array.forEach(_this.get_form().downGrid.outDetail, function (item) {
                            if (item.serialNo != null && item.serialNo.length > 0) {
                                item.serialNoList = item.serialNo;
                            }
                        });
                        Array.forEach(_this.get_form().downGrid.inDetail, function (item) {
                            if (item.serialNo != null && item.serialNo.length > 0) {
                                item.serialNoList = item.serialNo;
                            }
                        });
                        res.data.outDetail = _this.get_form().downGrid.outDetail;
                    }
                    // res.data.postState = 500;
                    // res.data.oldPostState = "PROCESS_COMPLETED";
                    // res.data.postState = "PROCESS_COMPLETED";
                    // if (audit) {
                    //     res.data.postState = "CONFIRM_WAIT";
                    // }
                    // res.data.onlyPost = false;
                    res.data.whetherPost = false;
                    // res.data.saveVipAsserts = false;
                    res.data.changeDate = false;
                    res.data.billSource = 'PC';
                    res.data.currencyBillTotal = (mainData.advanceGiftBillTotal - mainData.advanceGiftTotal).toFixed(2);
                    res.data.etypeId = mainData.etypeId;
                    res.data.efullname = mainData.efullname;
                    if (paymenyData.length != 0 && paymenyData[0].paywayId) {
                        res.data.payment = paymenyData;
                    }
                    res.data.billNumber = mainData.billNumber;
                    var requsetAudit = res.data;
                    var optionsT = {
                        url: "sale/shopsale/bill/submitGoodsBill",
                        data: {"goodsBill": res.data, "saveVipAsserts": false, "deletePreferential": false},
                        success: function (res, response) {
                            form.saveDetails.set_enabled(true);
                            if (res.code == "200" && res.data.resultType != "ERROR") {
                                $common.showTips("单据修改成功");
                                if (audit) {
                                    _this.doAniAudit(requsetAudit);
                                } else {
                                    _this.doRefresh(true);
                                }
                                _this.get_form().downGrid = null;
                            } else {
                                $common.showError(res.data.exceptionInfo[0].message);
                            }
                        }
                    }
                    _this.get_service().ajax(optionsT);

                }
            }
        }
        _this.get_service().ajax(options);
    },

    doAniAudit: function (data) {
        var queryParams = {};
        if (!data) {
            return;
        }
        queryParams.billNumber = data.billNumber;
        queryParams.vchtype = data.vchtype == "SaleBill" ? 2000 : 2100;
        queryParams.vchcode = data.vchcode;
        queryParams.postState = "CONFIRM_WAIT";
        var _this = this;

        this.get_service().post("jxc/recordsheet/billAudit/submitAudit", queryParams, function (rest) {
            if (rest.code == 200) {
                if (rest.data.successed) {
                    $common.alert("提交审核成功!", {showMDI: true});
                    //增加日志
                    //用户保存并提交审核,【单据编号:PXX-20230327-00994】
                    var requset = {};
                    requset = data;
                    requset.logMsg = "用户保存并提交审核,【单据编号:" + data.billNumber + "】";
                    _this.get_service().post("sale/shopsale/zyBill/addBillLogs", requset, function (rest) {

                    });
                    _this.get_form().downGrid = null;
                    _this.doTabChange();
                    _this.doRefresh();
                } else {
                    $common.alert(rest.data.message, {showMDI: true});
                }
            } else {
                $common.alert("提交失败，请联系管理员！", {showMDI: true});
            }

        })
    },


    timetrans: function (date) {
        var date = new Date(date);//如果date为10位需要乘1000
        var Y = date.getFullYear() + '-';
        var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
        var D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' ';
        var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
        var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
        var s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
        return Y + M + D + h + m + s;
    },


    doCellRender: function (sender, args) {
        var column = args.get_column();
        var field = column.get_dataField();
        var rowIndex = args.get_rowIndex();
        var rowData = sender.findRowData(rowIndex);
        if (!rowData) return; // 空行


        if (rowData.vchtype == 2100) {
            if (field === "orderPreferentialTotal") {
                args.set_style('background-color:#E5E5E5;color:#fff');
            }
            if (field === "scoreTotal") {
                args.set_style('background-color:#E5E5E5;color:#fff');
            }
            if (field === "couponsTotal") {
                args.set_style('background-color:#E5E5E5;color:#fff');
            }
            if (field === "orderAllTotal") {
                args.set_style('background-color:#E5E5E5;color:#fff');
            }

        }
    }, doGetFullNameText: function (sender, args) {
        var grid = sender.get_grid();
        var rowIndex = args.get_rowIndex();
        var rowData = grid.findRowData(rowIndex);
        if (!rowData) {
            return;
        }
        var str = args.get_text();
        var text = $shopsalecommon.getFullNameText(rowData, str);
        args.set_text(text);
        args._hint = str; // 提示文字保持不变
    },
    markDisplayText: function (sender, args) {
        var grid = sender.get_grid();
        var rowIndex = args.get_rowIndex();
        var rowData = grid.findRowData(rowIndex);
        if (!rowData) {
            return;
        }
        if (null != rowData.mark && rowData.mark.length > 0) {
            var spansHtml = ""; // 用一个临时变量存储所有 span 的 HTML
            for (var i = 0; i < rowData.mark.length; i++) {
                var itemMark = rowData.mark[i];

                var bgColor = itemMark.markColor.backgroundColor; // 获取背景颜色
                var bdColor = itemMark.markColor.borderColor;   // 获取边框颜色
                var ftColor = itemMark.markColor.fontColor;     // 获取字体颜色
                var showName = itemMark["markShowName"];        // 获取显示名称

                // 构建 span 的样式字符串
                // 移除了 float: left;
                // 添加了 display: inline-block; 使其可以被 text-align: center; 影响
                var styleString =
                    "display:inline-block;" +
                    "background:" + bgColor + ";" +
                    "border:1px solid " + bdColor + ";" +
                    "text-align:center;" +
                    "font-size:12px;" +
                    "color:" + ftColor + ";" +
                    "margin-left:1px;" +
                    "margin-top:4px;" +
                    "max-width:205px;" +
                    "border-radius:2px;" +
                    "line-height:20px;" +
                    "padding:0 3px;".trim();

                // 构建单个 span 标签的 HTML
                spansHtml += '<span style="' + styleString + '" title="' + showName + '">' + showName + '</span>';
            }

            // 在所有生成的 span 外面包裹一个 div，并给这个 div 添加 text-align: center; 样式
            var centeredHtml = '<div style="text-align: center;">' + spansHtml + '</div>';
            // 将包裹后的 HTML 设置到 args 中
            args.set_text(centeredHtml);
        }


    },


    doClickPtypeBtn1: function (sender, args) {
        var form = this.get_form();
        var rowData = form.grid.get_selectedRowData();
        var text = sender.get_text() ? sender.get_text() : "";
        var params = {}
        params["queryStr"] = text;
        params["multiSelect"] = false;
        params["selectType"] = "Sku";
        params["inputQry"] = false;
        params["existedSku"] = true;
        params["showTab"] = false;
        params["ptypeStoped"] = null;
        params.showStockQty = false;
        sender.set_showMDI(true);
        sender.set_selectorPage("jxc/recordsheet/selector/PtypeSelector.gspx");
        sender.set_selectorPageParams(params);
    }, doPTypeSelectorSelected1: function (sender, args) {
        var dataList = args.get_form().dataList;
        if (!dataList || dataList.length == 0) {
            return;
        }

        var form = this.get_form();
        var sku = dataList[0].sku
        form.edPType1.set_value(sku.id);
        form.edPType1.set_text(dataList[0].fullname);
    },


    _getDeliverGridSelectedItem: function (sender) {
        var form = sender.get_form();
        var grid = form.grid;
        var deliverBill = grid.get_selectedRowData();
        if (!deliverBill) return null;
        deliverBill.rowIndex = grid.get_selectedRowIndex();
        return deliverBill;
    }, showMember: function (sender) {
        var grid = sender.get_form().grid;
        var data = grid.get_selectedRowData();
        // if (data.stoped == 1) {
        //     $common.showTips("优惠券已停用");
        //     return;
        // }
        // 手机号要解密
        // var buyerIds = [];
        // buyerIds.push(data.buyerId);
        // var result = $shopsalecommon.batchDecryptBuyers(buyerIds);
        // // 这里只会有一条数据
        // if (result != null && result.length != 0) {
        //     data.vipPhone = result[0].customerReceiverPhone;
        // }

        $common.showPage(sender, "sale/member/pages/vip/manage/VipManage.gspx", {vipInfo: data}, SysConsts.CloseAll);
    },

    decryption: function (sender) {
        var grid = sender.get_form().grid;
        var data = grid.get_selectedRowData();
        // if (data.stoped == 1) {
        //     $common.showTips("优惠券已停用");
        //     return;
        // }
        // 手机号要解密
        var buyerIds = [];
        buyerIds.push(data.buyerId);
        var result = $shopsalecommon.batchDecryptBuyers(buyerIds);
        // 这里只会有一条数据
        if (result != null && result.length != 0) {
            data.vipPhone = result[0].customerReceiverPhone;
        }
        var index = grid.get_selectedRowIndex();
        grid.modifyRowData(index, data);
        // $common.showPage(sender, "sale/member/pages/vip/manage/VipManage.gspx", {vipInfo: data}, SysConsts.CloseAll);
    }, //对导出的数据提前处理
    doGetPrintData: function (sender, args) {
        var list = args._dataObj.params.data.detailData.itemList;
        var form = this.get_form();
        if (list.length > 0) {
            for (var i = 0; i < list.length; i++) {
                var item = list[i];
                // item.currencyBillTotal = item.currencyBillTotal ? math.abs(item.currencyBillTotal) : 0;
                // // item.balanceSettled = math.abs(item.balanceSettled);
                // // item.currencyBalanceRemain = math.abs(item.currencyBalanceRemain);
                // var ptypeQty = item.ptypeQty ? math.abs(item.ptypeQty) : 0;
                // var ptypeunitQty = item.ptypeunitQty ? math.abs(item.ptypeunitQty) : 0;
                // item.ptypeunitQty = form.deConformType.get_value() == 0 ? ptypeunitQty : ptypeQty;
                if (item.needInvoice != null && item.needInvoice == '否') {
                    this.clearInvoiceInfo(item);
                }
            }
        }
    },

    //计算价格
    calculatePrice: function (currencyDisedTaxedPrice, data) {
        // 小计 = 现价*数量-> 优惠前优惠 = 界面金额（单价*数量）-小计+优惠分摊->优惠前折扣：(小计-优惠分摊)/界面金额->最终折扣=小计/界面金额->最终优惠=界面金额-小计
        // 现价：currencyDisedTaxedPrice 小计：currencyDisedTaxedTotal 数量：unitQty 优惠分摊：currencyOrderPreferentialAllotTotal 优惠前折扣：discount 优惠前优惠：preferentialDiscount
        //单价：currencyPrice 最终优惠：currencyPreferentialTotal 最终折扣：lastDiscount 税率：taxRate 税额：currencyTaxTotal 折后不含税单价：currencyDisedPrice 折后不含税金额：currencyDisedTotal

        //折扣不含税单价=现价/（1+税率）
        //折扣不含税金额=折扣不含税单价*数量
        //税额=小计 -折扣不含税金额
        //界面金额
        var priceTotal = data.currencyPrice * data.unitQty;
        //小计
        var currencyDisedTaxedTotal = currencyDisedTaxedPrice * data.unitQty;
        //优惠前优惠
        var preferentialDiscount = priceTotal - currencyDisedTaxedTotal + data.currencyOrderPreferentialAllotTotal;
        //优惠前折扣
        var discount = (currencyDisedTaxedTotal - data.currencyOrderPreferentialAllotTotal) / priceTotal;
        //最终折扣
        var lastDiscount = currencyDisedTaxedTotal / priceTotal;
        //最终优惠
        var currencyPreferentialTotal = priceTotal - currencyDisedTaxedTotal;
        //折扣不含税单价
        var currencyDisedPrice = currencyDisedTaxedPrice / (1 + data.taxRate);
        //折扣不含税金额
        var currencyDisedTotal = currencyDisedPrice * data.unitQty;
        //税额
        var currencyTaxTotal = currencyDisedTaxedTotal - currencyDisedTotal;
        //回填
        data.currencyDisedTaxedTotal = currencyDisedTaxedTotal;
        data.preferentialDiscount = preferentialDiscount;
        data.discount = discount;
        data.lastDiscount = lastDiscount;
        data.currencyPreferentialTotal = currencyPreferentialTotal;
        data.currencyDisedPrice = currencyDisedPrice;
        data.currencyDisedTotal = currencyDisedTotal;
        data.currencyTaxTotal = currencyTaxTotal;
        return true;

    },

    doChangeProduceDate: function (sender, args) {
        // 目前只有入库明细有这种操作
        var form = sender.get_form();
        form.doRefreshBth = true;
        var rowData = args.get_rowData();
        var newValue = args.get_value();
        var date = new Date(newValue);
        date.setDate(date.getDate() + rowData.protectDays);
        form.grid1.modifyCellValue(args.get_rowIndex(), "expireDate", date.toISOString().substring(0, 10))
    },

    doInDetailSnInit: function (sender) {
        var grid = sender.get_owner();
        var selectRowData = grid.get_selectedRowData();
        var rowData = {};
        var ptype = selectRowData.ptype;
        if (ptype) {
            rowData.pFullName = ptype.fullName;
            rowData.ptypeId = ptype.id;
            rowData.snenabled = ptype.snEnabled;
            rowData.batchenabled = ptype.batchenabled;
            rowData.protectDays = ptype.protectDays;
            rowData.costMode = ptype.costMode;
        } else {
            rowData.pFullName = selectRowData.fullName == null ? selectRowData.pFullName : selectRowData.fullName;
            rowData.ptypeId = selectRowData.ptypeId;
            rowData.snenabled = selectRowData.snEnabled;
            rowData.batchenabled = selectRowData.batchenabled;
            rowData.protectDays = selectRowData.protectDays;
            rowData.costMode = selectRowData.costMode;
        }
        rowData.vchcode = selectRowData.vchcode;
        rowData.detailId = selectRowData.detailId;
        var serialNos = selectRowData.serialNo;
        if (serialNos != null && serialNos.length > 0) {
            for (var i = 0; i < serialNos.length; i++) {
                serialNos[i].snMemo = serialNos[i].snRemark;
            }
        }
        rowData.serialNoList = serialNos;
        rowData.ktypeId = selectRowData.ktypeId;
        rowData.skuId = selectRowData.skuId;
        rowData.batchNo = selectRowData.batchno;
        rowData.expireDate = selectRowData.expireDate;
        rowData.produceDate = selectRowData.produceDate;
        rowData.costId = selectRowData.costId;
        if (rowData.batchNo || rowData.expireDate || rowData.produceDate) {
            rowData.batchPrice = selectRowData.batchPrice;
        }

        var rowIndex = grid.get_selectedRowIndex();
        if (!rowData) return;
        var ktypeId = selectRowData.ktypeId;
        var ptypeList = [{
            rowIndex: rowIndex, rowData: rowData
        }];
        var vchcode = rowData.vchcode;
        sender.set_selectorPageParams({
            ptypeList: ptypeList, ktypeId: ktypeId, vchcode: vchcode, saveModel: "Save_MODIFY"
        });
        sender.set_selectorPage("jxc/recordsheet/selector/PtypeSerialNoInput.gspx?outType=false");
    },
};
sale.shopsale.pages.saleorder.SaleOrderQueryRetailAction.registerClass('sale.shopsale.pages.saleorder.SaleOrderQueryRetailAction', Sys.UI.PageAction);
